"use client";

import { useState, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Upload, Download, Loader2, X } from 'lucide-react';
import { toast } from 'sonner';
import Image from 'next/image';
import { v4 as uuidv4 } from 'uuid';
import { UserCredits } from '@/types/user';
import { processImagesWithWatermark } from '@/utils/watermark';

interface FreeAiHeadshotGeneratorProps {
  title?: string;
}

export default function FreeAiHeadshotGenerator({
  title = "Free AI Headshot Generator"
}: FreeAiHeadshotGeneratorProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State management
  const [mounted, setMounted] = useState(false);
  const [userCredits, setUserCredits] = useState<number | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [perceptionProgress, setPerceptionProgress] = useState(0);
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [actualProcessingStarted, setActualProcessingStarted] = useState(false);
  const [generationStartTime, setGenerationStartTime] = useState<number | null>(null);

  // Generation parameters
  const [gender, setGender] = useState<string>('none');
  const [background, setBackground] = useState<string>('neutral');
  const [aspectRatio, setAspectRatio] = useState<string>('match_input_image');
  const [outputFormat, setOutputFormat] = useState<string>('png');

  // Component mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch user credits
  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (!response.ok) {
        throw new Error('Failed to fetch user credits');
      }
      const data = await response.json();
      setUserCredits(data.left_credits);
    } catch (error) {
      console.error('Error fetching user credits:', error);
      setUserCredits(0);
    }
  };

  // Check session status when component loads
  useEffect(() => {
    if (session) {
      fetchUserCredits();
    }
  }, [session]);

  // Refresh credits after successful generation
  useEffect(() => {
    if (generatedImageUrl && session) {
      fetchUserCredits();
    }
  }, [generatedImageUrl, session]);

  // Sync perception progress when real progress updates (only after actual processing starts)
  useEffect(() => {
    if (actualProcessingStarted && progress > perceptionProgress) {
      setPerceptionProgress(progress);
    }
  }, [progress, perceptionProgress, actualProcessingStarted]);

  // Enhanced perception progress simulation with sqrt-based and segmented linear growth
  useEffect(() => {
    if (isGenerating) {
      const startTime = Date.now();
      setGenerationStartTime(startTime);

      const simulatedInterval = setInterval(() => {
        setPerceptionProgress(prev => {
          const elapsed = Date.now() - startTime;
          const waitingDuration = 40000; // 40秒等待时间

          let targetProgress;
          let increment;

          if (elapsed < waitingDuration) {
            // 🚀 准备阶段 (0% → 60%, 40秒) - 使用平方根函数实现非线性增长
            const progressRatio = elapsed / waitingDuration; // 0 到 1
            const sqrtProgress = Math.sqrt(progressRatio); // 平方根函数，前期快后期慢
            targetProgress = Math.min(sqrtProgress * 60, 60); // 映射到 0-60%

            // 动态计算增量，确保平滑过渡
            const timeDelta = 200; // 200ms更新间隔
            const nextProgressRatio = Math.min(1, (elapsed + timeDelta) / waitingDuration);
            const nextSqrtProgress = Math.sqrt(nextProgressRatio);
            const nextTargetProgress = Math.min(nextSqrtProgress * 60, 60);
            increment = Math.max(0.02, (nextTargetProgress - targetProgress) * 5); // 最小增量0.02%
          } else {
            // 🧠 生成阶段 (60% → 100%) - 使用分段线性增长策略
            if (!actualProcessingStarted) {
              // 等待真实处理开始，保持在60%
              targetProgress = 60;
              increment = 0;
            } else {
              // 真实处理已开始，从60%到100%的线性增长
              const processingElapsed = elapsed - waitingDuration;
              const processingDuration = 60000; // 预期60秒完成
              const processingProgress = Math.min(processingElapsed / processingDuration, 1);
              targetProgress = 60 + (processingProgress * 40); // 60% → 100%
              increment = 0.05 * (200 / 1000);
            }

            // 如果有真实进度且超过当前感知进度，使用真实进度
            if (progress > prev) {
              targetProgress = Math.max(prev, progress);
              increment = Math.max(increment, 0.5); // 真实进度时可以更快
            } else {
              targetProgress = Math.min(prev + increment, 100);
            }
          }

          // 计算新的进度值
          const newProgress = Math.min(prev + increment, targetProgress);

          // 确保进度只增不减，且不超过100%
          return Math.min(Math.max(prev, newProgress), 100);
        });
      }, 200); // 200ms更新间隔确保视觉流畅

      return () => {
        clearInterval(simulatedInterval);
        setActualProcessingStarted(false);
        setGenerationStartTime(null);
      };
    }
  }, [isGenerating, progress, actualProcessingStarted]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setSelectedFile(file);

    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // Clear previous results and reset progress
    setGeneratedImageUrl('');
    setProgress(0);
    setPerceptionProgress(0);
    setActualProcessingStarted(false);
  };

  // Handle drag and drop
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      const fakeEvent = {
        target: { files: [file] }
      } as unknown as React.ChangeEvent<HTMLInputElement>;
      handleFileSelect(fakeEvent);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  // Clear selected image and results
  const clearImage = () => {
    setSelectedFile(null);
    setPreviewUrl('');
    setGeneratedImageUrl('');
    setProgress(0);
    setPerceptionProgress(0);
    setActualProcessingStarted(false);

    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Upload image to R2 storage
  const uploadImageToR2 = async (file: File): Promise<string> => {
    setIsUploading(true);
    try {
      // Convert file to base64 for upload
      const reader = new FileReader();
      const base64Promise = new Promise<string>((resolve) => {
        reader.onload = () => {
          const result = reader.result as string;
          resolve(result);
        };
      });
      reader.readAsDataURL(file);
      const base64Data = await base64Promise;

      // Generate unique filename
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const filename = `headshot-input-${uuidv4()}.${fileExtension}`;

      // Upload to R2 via API
      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceUrl: base64Data,
          filename: filename,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const data = await response.json();
      return data.url;
    } finally {
      setIsUploading(false);
    }
  };

  // Generate headshot
  const generateHeadshot = async () => {
    if (!session?.user) {
      toast.error('Please sign in to generate headshots');
      router.push('/auth/signin');
      return;
    }

    if (!selectedFile) {
      toast.error('Please select an image first');
      return;
    }

    // Check if user has enough credits
    if (userCredits !== null && userCredits < 5) {
      toast.error('Insufficient credits. Each headshot generation requires 5 credits');
      router.push('/pricing');
      return;
    }

    // Reset progress states and start generation
    setProgress(0);
    setPerceptionProgress(0);
    setActualProcessingStarted(false);
    setGeneratedImageUrl('');
    setIsGenerating(true);

    // Wait 40 seconds before starting actual processing
    setTimeout(() => {
      performActualGeneration();
    }, 40000);
  };

  // Actual headshot generation processing function
  const performActualGeneration = async () => {
    setActualProcessingStarted(true);
    setProgress(60); // Start real progress from 60%

    try {
      const taskId = `headshot_${uuidv4()}`;

      // Step 1: Deduct credits first
      setProgress(61);
      try {
        const deductResponse = await fetch('/api/deduct-credits', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            credits: 5, // Deduct 5 credits for headshot generation
            taskId: taskId,
          }),
        });

        if (!deductResponse.ok) {
          const errorData = await deductResponse.json();
          if (errorData.error === "insufficient_credits") {
            router.push('/pricing');
            return;
          }
          throw new Error(errorData.error || 'Failed to deduct credits');
        }

        const creditData = await deductResponse.json();
        setUserCredits(creditData.creditsLeft);
        setProgress(65);
      } catch (creditError) {
        console.error('Failed to deduct credits:', creditError);
        setIsGenerating(false);
        return;
      }

      // Step 2: Upload image
      setProgress(67);
      const uploadedImageUrl = await uploadImageToR2(selectedFile!);
      setProgress(70);

      // Step 3: Generate headshot
      setProgress(75);

      const response = await fetch('/api/headshot-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: uploadedImageUrl,
          gender,
          background,
          aspectRatio,
          outputFormat,
          safetyTolerance: 2,
          taskId
        }),
      });

      setProgress(85);

      if (!response.ok) {
        const errorData = await response.json();
        // Refund credits on failure
        await refundCredits(taskId);
        throw new Error(errorData.error || 'Failed to generate headshot');
      }

      const data = await response.json();
      setProgress(95);

      if (!data.imageUrl) {
        await refundCredits(taskId);
        throw new Error('No image URL received');
      }

      console.log('Generated headshot URL:', data.imageUrl);

      // Process watermark based on user credits
      setProgress(96);
      try {
        console.log('🎯 Starting watermark processing for headshot...');
        const processedImages = await processImagesWithWatermark([data.imageUrl], taskId);
        console.log('🎯 Watermark processing completed, setting image...');

        if (processedImages && processedImages.length > 0) {
          setGeneratedImageUrl(processedImages[0]);
        } else {
          setGeneratedImageUrl(data.imageUrl);
        }
      } catch (watermarkError) {
        console.error('❌ Watermark processing failed:', watermarkError);
        // If watermark fails, still show the original image
        setGeneratedImageUrl(data.imageUrl);
      }

      setProgress(100);
      toast.success('Professional headshot generated successfully!');

    } catch (error) {
      console.error('Generation error:', error);
      setProgress(0);
      setPerceptionProgress(0);
    } finally {
      setIsGenerating(false);
    }
  };

  // Refund credits function
  const refundCredits = async (taskId: string) => {
    try {
      const response = await fetch('/api/refund-credits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          credits: 5,
          taskId: taskId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.creditsLeft);
        console.log('Generation failed, 5 credits have been automatically refunded');
      }
    } catch (error) {
      console.error('Failed to refund credits:', error);
    }
  };

  // Download generated image
  const downloadImage = async () => {
    if (!generatedImageUrl) return;

    try {
      console.log('📥 Downloading headshot image:', generatedImageUrl.substring(0, 50) + '...');

      // Check if it's a base64 data URL (watermarked image)
      if (generatedImageUrl.startsWith('data:image/')) {
        console.log('📥 Downloading base64 watermarked image');
        const link = document.createElement('a');
        link.href = generatedImageUrl;
        link.download = 'ai-professional-headshot-watermarked.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success('Download started successfully!');
      } else {
        // For HTTP URLs, use proxy with download parameter
        console.log('📥 Downloading HTTP image via proxy');
        const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(generatedImageUrl)}&download=true`;
        const link = document.createElement('a');
        link.href = proxyUrl;
        link.download = 'ai-professional-headshot.png';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success('Download started successfully!');
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download image');
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto py-4 md:py-6 space-y-6 md:space-y-8">
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">{title}</h2>
        <p className="text-base md:text-lg text-muted-foreground">
          Transform your casual photos into professional headshots with AI
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8">
        {/* Input Section */}
        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6 space-y-4 md:space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
              <h3 className="text-lg md:text-xl font-semibold">Upload Your Photo</h3>
              <div className="bg-blue-100 text-blue-600 px-2 md:px-3 py-1 rounded-full flex items-center gap-1 md:gap-2 self-start sm:self-auto">
                <span className="text-xs md:text-sm font-normal">Credits:</span>{" "}
                {mounted && session ? (
                  <span className="text-sm md:text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
                ) : (
                  <span className="text-sm md:text-lg font-bold">
                    <a href="/auth/signin" className="hover:underline">Sign in</a>
                  </span>
                )}
                <svg
                  className="w-3 h-3 md:w-4 md:h-4 text-amber-500 fill-current"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0-15c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h12c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9zm0 3c3.31 0 6 2.69 6 6v7c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-7c0-3.31 2.69-6 6-6z" />
                </svg>
              </div>
            </div>
            
            {/* File Upload Area */}
            <div
              className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-3 sm:p-4 md:p-8 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
              
              {previewUrl ? (
                <div className="space-y-3 md:space-y-4">
                  <div className="relative w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 mx-auto">
                    <Image
                      src={previewUrl}
                      alt="Preview"
                      fill
                      className="object-cover rounded-lg"
                    />
                    {/* Clear image button */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        clearImage();
                      }}
                      className="absolute -top-1 -right-1 md:-top-2 md:-right-2 w-5 h-5 md:w-6 md:h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors"
                      title="Remove image"
                    >
                      <X className="w-3 h-3 md:w-4 md:h-4" />
                    </button>
                  </div>
                  <p className="text-xs md:text-sm text-muted-foreground px-2">
                    Click to change image or use the × button to remove
                  </p>
                </div>
              ) : (
                <div className="space-y-3 md:space-y-4">
                  <Upload className="w-8 h-8 md:w-12 md:h-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-base md:text-lg font-medium">Drop your image here</p>
                    <p className="text-xs md:text-sm text-muted-foreground">
                      or click to browse (max 10MB)
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Generation Parameters */}
            <div className="space-y-3 md:space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 md:gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  <Select value={gender} onValueChange={setGender}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="background">Background</Label>
                  <Select value={background} onValueChange={setBackground}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="neutral">Neutral</SelectItem>
                      <SelectItem value="white">White</SelectItem>
                      <SelectItem value="black">Black</SelectItem>
                      <SelectItem value="gray">Gray</SelectItem>
                      <SelectItem value="office">Office</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 md:gap-4">
                <div className="space-y-2">
                  <Label htmlFor="aspectRatio">Aspect Ratio</Label>
                  <Select value={aspectRatio} onValueChange={setAspectRatio}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="match_input_image">Match Input Image</SelectItem>
                      <SelectItem value="1:1">Square (1:1)</SelectItem>
                      <SelectItem value="16:9">Widescreen (16:9)</SelectItem>
                      <SelectItem value="9:16">Vertical (9:16)</SelectItem>
                      <SelectItem value="4:3">Standard (4:3)</SelectItem>
                      <SelectItem value="3:4">Portrait (3:4)</SelectItem>
                      <SelectItem value="3:2">Classic (3:2)</SelectItem>
                      <SelectItem value="2:3">Tall Portrait (2:3)</SelectItem>
                      <SelectItem value="4:5">Social Media (4:5)</SelectItem>
                      <SelectItem value="5:4">Landscape (5:4)</SelectItem>
                      <SelectItem value="21:9">Ultra Wide (21:9)</SelectItem>
                      <SelectItem value="9:21">Ultra Tall (9:21)</SelectItem>
                      <SelectItem value="2:1">Panoramic (2:1)</SelectItem>
                      <SelectItem value="1:2">Vertical Banner (1:2)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="outputFormat">Format</Label>
                  <Select value={outputFormat} onValueChange={setOutputFormat}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="jpg">JPG</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Generate Button */}
            <Button
              onClick={generateHeadshot}
              disabled={!selectedFile || isGenerating || isUploading}
              className="w-full text-sm md:text-base"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {perceptionProgress > 0 ? `Generating Headshot... ${Math.round(perceptionProgress)}%` : `Generating Headshot...`}
                </>
              ) : isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Generate Professional Headshot'
              )}
            </Button>

            {/* Progress Bar */}
            {isGenerating && (
              <>
                <div className="w-full bg-muted rounded-full h-2 md:h-2.5 mt-2 overflow-hidden">
                  <div
                    className="bg-blue-600 h-2 md:h-2.5 rounded-full transition-all duration-300"
                    style={{ width: `${perceptionProgress}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-[shimmer_2s_infinite]"></div>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground text-center mt-1">
                  Generating...
                </p>
                <p className="text-xs md:text-sm text-red-600 text-center mt-2 font-medium px-2">
                  Your headshot is being generated. This typically takes 20s-2 minutes. Please don't close this page.
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Result Section */}
        <Card>
          <CardContent className="p-3 sm:p-4 md:p-6 space-y-4 md:space-y-6">
            <h3 className="text-lg md:text-xl font-semibold">Professional Headshot Result</h3>
            
            <div className="relative aspect-square w-full bg-muted rounded-lg overflow-hidden">
              {generatedImageUrl ? (
                <WatermarkedImage
                  src={generatedImageUrl}
                  alt="Generated professional headshot"
                  fill
                  className="object-cover"
                  priority
                  userCredits={userCredits || 0}
                  method="canvas"
                  onError={() => {
                    console.error('Image failed to load:', generatedImageUrl);
                    // Try to reload the image or show error message
                  }}
                  onLoad={() => {
                    console.log('Image loaded successfully:', generatedImageUrl);
                  }}
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center p-4">
                  <p className="text-muted-foreground text-center text-sm md:text-base">
                    {isGenerating ? 'Generating...' : 'Your professional headshot will appear here'}
                  </p>
                </div>
              )}
            </div>

            {/* Download Button */}
            {generatedImageUrl && (
              <Button
                onClick={downloadImage}
                className="w-full text-sm md:text-base"
                size="lg"
                variant="outline"
              >
                <Download className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Download Professional Headshot</span>
                <span className="sm:hidden">Download Headshot</span>
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
