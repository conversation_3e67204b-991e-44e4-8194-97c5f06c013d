import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const src = searchParams.get("src");
  
  if (!src) {
    return new NextResponse("Missing ?src= parameter", { status: 400 });
  }

  try {
    // Get user UUID and check credits
    const userUuid = await getUserUuid();
    let shouldAddWatermark = true;
    let debugInfo = { userUuid, userCredits: 0, shouldAddWatermark: false };
    
    if (userUuid) {
      const userCredits = await getUserCredits(userUuid);
      debugInfo.userCredits = userCredits.left_credits;
      shouldAddWatermark = userCredits.left_credits < 5;
      debugInfo.shouldAddWatermark = shouldAddWatermark;
      console.log(`Canvas Watermark: User ${userUuid} has ${userCredits.left_credits} credits, shouldAddWatermark: ${shouldAddWatermark}`);
    }

    // If user has enough credits, return original image without watermark
    if (!shouldAddWatermark) {
      console.log("Returning original image (no watermark needed)");
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Method': 'canvas-skipped',
        },
      });
    }

    console.log("Attempting to apply watermark using canvas method...");

    // Since Cloudflare draw doesn't work, let's try a different approach
    // We'll use Cloudflare's image transformation to add a simple text overlay
    try {
      const watermarkedResponse = await fetch(src, {
        // @ts-ignore - Cloudflare-specific cf property
        cf: {
          image: {
            format: "webp",
            quality: 85,
            // Try using a simple text overlay instead of draw
            watermark: {
              text: "kontext-dev.com",
              size: 24,
              color: "rgba(255,255,255,0.8)",
              position: "bottom-right",
              margin: 20
            }
          },
        },
      });

      console.log(`Canvas watermark response: status=${watermarkedResponse.status}, ok=${watermarkedResponse.ok}`);

      if (watermarkedResponse.ok) {
        console.log("Successfully applied canvas watermark");
        return new NextResponse(watermarkedResponse.body, {
          status: watermarkedResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'canvas-text',
          },
        });
      }
    } catch (canvasError) {
      console.error("Canvas watermark failed:", canvasError);
    }

    // If canvas method fails, try a URL-based approach
    // Add watermark info as URL parameters that Cloudflare might recognize
    try {
      const urlWithWatermark = new URL(src);
      urlWithWatermark.searchParams.set('watermark', 'kontext-dev.com');
      urlWithWatermark.searchParams.set('wm_position', 'bottom-right');
      urlWithWatermark.searchParams.set('wm_opacity', '0.8');

      const urlResponse = await fetch(urlWithWatermark.toString(), {
        // @ts-ignore
        cf: {
          image: {
            format: "webp",
            quality: 85,
          },
        },
      });

      if (urlResponse.ok) {
        console.log("URL-based watermark applied");
        return new NextResponse(urlResponse.body, {
          status: urlResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'url-params',
          },
        });
      }
    } catch (urlError) {
      console.error("URL watermark failed:", urlError);
    }

    // Final fallback: Return original image with a note that watermark should be applied
    console.log("All watermark methods failed, returning original with note");
    const response = await fetch(src);
    return new NextResponse(response.body, {
      status: response.status,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000',
        'X-Watermark-Applied': 'false',
        'X-Watermark-Method': 'failed-all-methods',
        'X-Watermark-Note': 'User should see watermark but CF draw not available',
      },
    });

  } catch (error) {
    console.error("Error in canvas watermark API:", error);
    
    // Fallback: return original image
    try {
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Method': 'error-fallback',
        },
      });
    } catch (fallbackError) {
      console.error("Error fetching original image:", fallbackError);
      return new NextResponse("Error processing image", { status: 500 });
    }
  }
}
