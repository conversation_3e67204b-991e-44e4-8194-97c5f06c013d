import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";

export async function GET(req: NextRequest) {
  try {
    // 检查用户认证
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const taskId = searchParams.get('taskId');

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    console.log(`Fetching watermarked images for task: ${taskId}, user: ${userUuid}`);

    const supabase = getSupabaseClient();
    
    // 查询数据库中的水印图片URL
    const { data, error } = await supabase
      .from("4o_generations")
      .select("generated_image_url, status")
      .eq("task_id", taskId)
      .eq("user_uuid", userUuid)
      .single();

    if (error) {
      console.error('Database query error:', error);
      return NextResponse.json(
        { error: 'Database query failed', details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'No record found for this task' },
        { status: 404 }
      );
    }

    console.log('Database record found:', {
      status: data.status,
      hasGeneratedUrl: !!data.generated_image_url,
      urlPrefix: data.generated_image_url ? data.generated_image_url.substring(0, 50) + '...' : 'null'
    });

    // 检查是否有生成的图片URL
    if (!data.generated_image_url) {
      return NextResponse.json(
        { error: 'No generated image URL found in database' },
        { status: 404 }
      );
    }

    // 检查URL是否是水印图片（包含watermarked前缀）
    const isWatermarked = data.generated_image_url.includes('watermarked-');
    
    if (!isWatermarked) {
      console.log('⚠️ Found non-watermarked image URL in database for low-credit user');
      return NextResponse.json(
        { error: 'No watermarked image available' },
        { status: 404 }
      );
    }

    console.log('✅ Returning watermarked image URL:', data.generated_image_url);

    return NextResponse.json({
      success: true,
      watermarkedUrls: [data.generated_image_url],
      status: data.status,
      taskId: taskId
    });

  } catch (error) {
    console.error('Error fetching watermarked images:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
