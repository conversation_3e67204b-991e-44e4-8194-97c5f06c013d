我的网站是部署到cloudflare pages，使用的是next.js搭建的，图片生成的方法是调用replicate api生成图片。

我需要给生成的图片添加“平铺式防盗水印”，只有用户积分大于5，才能得到无水印的图片，否则，始终显示有“平铺式防盗水印”的是图片。

什么是“平铺式防盗水印”：在整张图上铺一层斜向网格（常见是 45° 斜线），再在角落叠一块品牌/域名角标（这里是kontext-dev.com）。这类水印俗称“stock/preview watermark”。

我查阅了资料，：
在 Cloudflare Pages 上用 Pages Functions（Workers 运行时） 给 Replicate 生成的图片做二次处理，直接在边缘节点把水印叠上去即可。Cloudflare 的图片变换支持在 fetch() 里加 cf.image.draw，可以把一张小的水印纹理平铺、旋转、设置透明度；也能再叠一个角标 logo。这个能力只在 Workers/Pages Functions 中提供。

下面给你一份最小可用的示例（TypeScript），放到项目根目录的 /functions/wm.ts（或 .js）即可：

// /functions/wm.ts
export const onRequestGet: PagesFunction = async (ctx) => {
  const { searchParams } = new URL(ctx.request.url);
  const src = searchParams.get("src"); // 传入 Replicate 返回的图片 URL
  if (!src) return new Response("Missing ?src=", { status: 400 });

  // 你的水印资源：建议放在项目静态目录、R2 或 KV 上
  const TILE = "https://your-domain.com/assets/wm-tile.png"; // 透明PNG，小方块里画一根或两根斜线
  const BADGE = "https://your-domain.com/assets/wm-badge.png"; // 角标logo/域名

  // 直接在边缘做图像变换并返回
  return fetch(src, {
    cf: {
      image: {
        // 可选：统一尺寸/编码，例如 width: 1200, format: "webp"
        draw: [
          // 1) 斜线纹理：整图平铺+旋转+半透明
          { url: TILE, repeat: true, opacity: 0.18, rotate: 45 },
          // 2) 右下角角标
          { url: BADGE, bottom: 12, right: 12, width: 140, height: 36, fit: "contain", opacity: 0.92 }
        ],
      },
    },
  });
};



使用方式：
前端拿到 Replicate 的图片地址后，请求你自己的接口：/wm?src=<replicate图片URL>，返回的就是已经打好水印的图片。

资源链接：
https://developers.cloudflare.com/images/transform-images/draw-overlays/

https://developers.cloudflare.com/images/transform-images/bindings/?utm_source=chatgpt.com


实现要点与小贴士

做一张 128–256px 的透明 PNG 作为“纹理砖”（里面画一两根斜线），用 repeat:true + rotate:45 + opacity 就能得到图里这种斜向网格效果。Cloudflare 官方就推荐这样通过 draw 平铺做“stock photo 水印”。

如果你的图片放在 R2/KV/Cloudflare Images，也可以用 Images binding 的 .draw() API 在 Worker 里叠水印（同样支持 repeat/opacity/rotate），适合批量/后台流水线处理。

由于 Pages Functions 运行在 Workers 里，无需 node-native 库（如 sharp）；cf.image.draw 就能完成叠图、平铺、透明度、旋转等操作。

想保护原图的话，把原图放 R2/私有地址，只对外暴露这个带水印的接口版本。


我现在在本地电脑中准备了45度斜线纹理和角标logo：
watermark_tile.png
kontext-dev.com_badge.png

现在请你帮我实现怎么给生成的图片添加“平铺式防盗水印”，详细教我怎么操作。

## 实现步骤

### 1. 创建 functions 目录和水印处理函数
### 2. 上传水印资源文件到 public 目录
### 3. 修改图片生成 API 集成水印功能
### 4. 根据用户积分决定是否显示水印