import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const src = searchParams.get("src");
  
  if (!src) {
    return new NextResponse("Missing ?src= parameter", { status: 400 });
  }

  try {
    // Check user credits
    const userUuid = await getUserUuid();
    let shouldAddWatermark = true;
    
    if (userUuid) {
      const userCredits = await getUserCredits(userUuid);
      shouldAddWatermark = userCredits.left_credits < 5;
    }

    // If user has enough credits, return original image
    if (!shouldAddWatermark) {
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
        },
      });
    }

    // Apply watermark using external service
    // Option 1: Use a watermark service API
    const watermarkServiceUrl = `https://api.watermarkservice.com/v1/watermark`;
    
    const watermarkResponse = await fetch(watermarkServiceUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.WATERMARK_API_KEY}`,
      },
      body: JSON.stringify({
        source_image: src,
        watermark_type: 'tiled_grid',
        opacity: 0.3,
        rotation: 45,
        brand_text: 'kontext-dev.com',
        brand_position: 'bottom-left',
        output_format: 'webp'
      })
    });

    if (watermarkResponse.ok) {
      return new NextResponse(watermarkResponse.body, {
        status: watermarkResponse.status,
        headers: {
          'Content-Type': 'image/webp',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'true',
          'X-Watermark-Method': 'external-service',
        },
      });
    }

    // Fallback: Use URL-based watermark service
    const fallbackUrl = `https://images.weserv.nl/?url=${encodeURIComponent(src)}&overlay=https://kontext-dev.com/watermark-overlay.png&oa=30&or=45`;
    
    const fallbackResponse = await fetch(fallbackUrl);
    return new NextResponse(fallbackResponse.body, {
      status: fallbackResponse.status,
      headers: {
        'Content-Type': 'image/webp',
        'Cache-Control': 'public, max-age=31536000',
        'X-Watermark-Applied': 'true',
        'X-Watermark-Method': 'url-service',
      },
    });

  } catch (error) {
    console.error("Error applying watermark:", error);
    
    // Final fallback: return original image
    const response = await fetch(src);
    return new NextResponse(response.body, {
      status: response.status,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000',
        'X-Watermark-Applied': 'false',
        'X-Watermark-Error': 'service-unavailable',
      },
    });
  }
}
