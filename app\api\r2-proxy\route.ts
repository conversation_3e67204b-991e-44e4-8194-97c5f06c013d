import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const imageUrl = searchParams.get("url");

  if (!imageUrl) {
    return new NextResponse("Missing ?url= parameter", { status: 400 });
  }

  // Validate that it's an R2 URL from our domain
  try {
    const url = new URL(imageUrl);
    if (!url.hostname.endsWith('.kontext-dev.com') && url.hostname !== 'kontext-dev.com') {
      return new NextResponse("Only kontext-dev.com domain images are allowed", { status: 403 });
    }
  } catch (error) {
    return new NextResponse("Invalid URL", { status: 400 });
  }

  try {
    console.log("R2 proxy: Fetching image from:", imageUrl);

    // Fetch the image from R2
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'kontext-dev.com/1.0',
        'Accept': 'image/*',
      },
    });

    if (!response.ok) {
      console.error(`R2 proxy: Failed to fetch image: ${response.status} ${response.statusText}`);
      return new NextResponse(`Failed to fetch image: ${response.status} ${response.statusText}`, { 
        status: response.status 
      });
    }

    const contentType = response.headers.get('Content-Type') || 'image/jpeg';
    const contentLength = response.headers.get('Content-Length');

    console.log(`R2 proxy: Successfully fetched image, Content-Type: ${contentType}, Size: ${contentLength} bytes`);

    // Return the image with proper CORS headers for Canvas
    return new NextResponse(response.body, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
        'Access-Control-Allow-Origin': 'https://kontext-dev.com',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Credentials': 'true',
        'X-R2-Proxy': 'true',
        'X-Original-URL': imageUrl,
      },
    });

  } catch (error) {
    console.error("R2 proxy error:", error);
    return new NextResponse(
      `Error fetching R2 image: ${error instanceof Error ? error.message : String(error)}`, 
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': 'https://kontext-dev.com',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400',
    },
  });
}
