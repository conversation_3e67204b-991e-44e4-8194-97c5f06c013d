import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const src = searchParams.get("src");
  const force = searchParams.get("force") === "true";

  if (!src) {
    return new NextResponse("Missing ?src= parameter", { status: 400 });
  }

  try {
    // Get user UUID and check credits
    const userUuid = await getUserUuid();
    let shouldAddWatermark = true;

    if (!force && userUuid) {
      const userCredits = await getUserCredits(userUuid);
      // Only add watermark if user has fewer than 5 credits
      shouldAddWatermark = userCredits.left_credits < 5;

      console.log(`SVG watermark: User ${userUuid} has ${userCredits.left_credits} credits, shouldAddWatermark: ${shouldAddWatermark}`);
    } else if (!userUuid) {
      // No user logged in, should show watermark
      shouldAddWatermark = true;
      console.log("SVG watermark: No user logged in, showing watermark");
    }

    // If user has enough credits and not forced, return original image
    if (!shouldAddWatermark && !force) {
      console.log("SVG watermark: Returning original image (no watermark needed)");
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Reason': 'User has sufficient credits',
          'X-Watermark-Method': 'svg-skipped',
        },
      });
    }

    console.log("SVG watermark: Applying watermark using SVG overlay...");

    // Create SVG watermark overlay
    const svgWatermark = createSVGWatermark();
    
    // Encode SVG as base64 for use as overlay
    const svgBase64 = btoa(svgWatermark);
    const svgDataUrl = `data:image/svg+xml;base64,${svgBase64}`;

    // Try using weserv.nl with SVG overlay
    try {
      const watermarkParams = new URLSearchParams({
        url: src,
        overlay: svgDataUrl,
        oa: '80', // overlay opacity (80%)
        output: 'webp',
        q: '85'
      });

      const weservUrl = `https://images.weserv.nl/?${watermarkParams.toString()}`;
      console.log(`SVG watermark - Weserv URL: ${weservUrl.substring(0, 200)}...`);

      const watermarkedResponse = await fetch(weservUrl);
      console.log(`SVG watermark - Weserv response: status=${watermarkedResponse.status}, ok=${watermarkedResponse.ok}`);

      if (watermarkedResponse.ok) {
        console.log("Successfully applied SVG watermark using weserv.nl");
        return new NextResponse(watermarkedResponse.body, {
          status: watermarkedResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'svg-overlay',
            'X-Force-Watermark': force ? 'true' : 'false',
          },
        });
      }
    } catch (svgError) {
      console.error("SVG overlay watermark failed:", svgError);
    }

    // Fallback to simple text watermark
    try {
      const textParams = new URLSearchParams({
        url: src,
        txt: 'kontext-dev.com',
        txtsize: '28',
        txtcolor: 'ffffff',
        txtbg: '0096ffcc', // Blue with transparency
        txtpad: '12',
        txtpos: 'bottom-left',
        output: 'webp',
        q: '85'
      });

      const textUrl = `https://images.weserv.nl/?${textParams.toString()}`;
      console.log(`SVG watermark - Text fallback URL: ${textUrl}`);

      const textResponse = await fetch(textUrl);
      console.log(`SVG watermark - Text response: status=${textResponse.status}, ok=${textResponse.ok}`);

      if (textResponse.ok) {
        console.log("Successfully applied text watermark fallback");
        return new NextResponse(textResponse.body, {
          status: textResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'svg-text-fallback',
            'X-Force-Watermark': force ? 'true' : 'false',
          },
        });
      }
    } catch (textError) {
      console.error("Text watermark fallback failed:", textError);
    }

    // Final fallback: return original image
    console.log("All SVG watermark methods failed, falling back to original image");
    const response = await fetch(src);
    return new NextResponse(response.body, {
      status: response.status,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000',
        'X-Watermark-Applied': 'false',
        'X-Watermark-Reason': 'All SVG watermark methods failed',
      },
    });

  } catch (error) {
    console.error("SVG watermark error:", error);

    // Fallback: return original image
    try {
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Reason': 'SVG watermark error',
          'X-Watermark-Error': error instanceof Error ? error.message : String(error),
        },
      });
    } catch (fallbackError) {
      console.error("SVG watermark fallback error:", fallbackError);
      return new NextResponse("Error processing image", { status: 500 });
    }
  }
}

function createSVGWatermark(): string {
  return `
    <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse" patternTransform="rotate(45)">
          <rect width="100" height="100" fill="none"/>
          <text x="50" y="50" text-anchor="middle" dominant-baseline="middle" 
                fill="rgba(0,150,255,0.1)" font-family="Arial, sans-serif" font-size="12" font-weight="bold">
            kontext-dev.com
          </text>
        </pattern>
      </defs>
      
      <!-- Grid pattern overlay -->
      <rect width="100%" height="100%" fill="url(#grid)" opacity="0.3"/>
      
      <!-- Main watermark in bottom-left -->
      <g transform="translate(20, 560)">
        <rect x="-10" y="-35" width="200" height="40" rx="5" 
              fill="rgba(0,150,255,0.8)" stroke="rgba(0,150,255,1)" stroke-width="1"/>
        <text x="0" y="-10" fill="white" font-family="Arial, sans-serif" 
              font-size="24" font-weight="bold">kontext-dev.com</text>
      </g>
      
      <!-- Corner watermarks -->
      <text x="20" y="30" fill="rgba(0,150,255,0.6)" font-family="Arial, sans-serif" 
            font-size="16" font-weight="bold">kontext-dev.com</text>
      <text x="780" y="30" text-anchor="end" fill="rgba(0,150,255,0.6)" 
            font-family="Arial, sans-serif" font-size="16" font-weight="bold">kontext-dev.com</text>
      <text x="780" y="590" text-anchor="end" fill="rgba(0,150,255,0.6)" 
            font-family="Arial, sans-serif" font-size="16" font-weight="bold">kontext-dev.com</text>
    </svg>
  `.trim();
}
