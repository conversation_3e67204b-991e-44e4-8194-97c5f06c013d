import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const src = searchParams.get("src");

  if (!src) {
    return new NextResponse("Missing ?src= parameter", { status: 400 });
  }

  try {
    console.log("Force watermark API called with src:", src);

    // Method 1: Use images.weserv.nl with text overlay
    try {
      // Create watermarked image URL using weserv.nl
      const watermarkParams = new URLSearchParams({
        url: src,
        // Add text watermark
        txt: 'kontext-dev.com',
        txtsize: '24',
        txtcolor: 'ffffff',
        txtbg: '0096ff',
        txtpad: '10',
        txtpos: 'bottom-left',
        // Output format
        output: 'webp',
        q: '85'
      });

      const weservUrl = `https://images.weserv.nl/?${watermarkParams.toString()}`;
      console.log(`Force watermark - Weserv URL: ${weservUrl}`);

      const watermarkedResponse = await fetch(weservUrl);
      console.log(`Force watermark - Weserv response: status=${watermarkedResponse.status}, ok=${watermarkedResponse.ok}`);

      if (watermarkedResponse.ok) {
        console.log("Successfully applied FORCE watermark using weserv.nl");
        return new NextResponse(watermarkedResponse.body, {
          status: watermarkedResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'images.weserv.nl-force',
            'X-Force-Watermark': 'true',
          },
        });
      }
    } catch (weservError) {
      console.error("Error with force weserv.nl:", weservError);
    }

    // Method 2: Fallback to simple text overlay
    try {
      const simpleWatermarkParams = new URLSearchParams({
        url: src,
        txt: 'kontext-dev.com',
        txtsize: '20',
        txtcolor: 'ffffff',
        txtbg: '000000aa',
        txtpad: '8',
        txtpos: 'bottom-left',
        output: 'webp',
        q: '80'
      });

      const simpleWeservUrl = `https://images.weserv.nl/?${simpleWatermarkParams.toString()}`;
      console.log(`Force watermark - Simple weserv URL: ${simpleWeservUrl}`);

      const simpleResponse = await fetch(simpleWeservUrl);
      console.log(`Force watermark - Simple weserv response: status=${simpleResponse.status}, ok=${simpleResponse.ok}`);

      if (simpleResponse.ok) {
        console.log("Successfully applied FORCE simple watermark using weserv.nl");
        return new NextResponse(simpleResponse.body, {
          status: simpleResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'images.weserv.nl-simple-force',
            'X-Force-Watermark': 'true',
          },
        });
      }
    } catch (simpleError) {
      console.error("Error with force simple weserv.nl:", simpleError);
    }

    // Fallback: return original image if all watermark methods fail
    console.log("All FORCE watermark methods failed, falling back to original image");
    const response = await fetch(src);
    return new NextResponse(response.body, {
      status: response.status,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000',
        'X-Watermark-Applied': 'false',
        'X-Watermark-Reason': 'All force watermark methods failed',
        'X-Force-Watermark': 'attempted',
      },
    });

  } catch (error) {
    console.error("Error processing FORCE watermark request:", error);

    // Fallback: return original image if anything fails
    try {
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Reason': 'General error in force watermark',
          'X-Force-Watermark': 'error',
        },
      });
    } catch (fallbackError) {
      console.error("Error fetching original image in force watermark:", fallbackError);
      return new NextResponse("Error processing image", { status: 500 });
    }
  }
}
