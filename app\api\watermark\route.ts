import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const src = searchParams.get("src"); // Original image URL from Replicate
  
  if (!src) {
    return new NextResponse("Missing ?src= parameter", { status: 400 });
  }

  try {
    // Get user UUID and check credits
    const userUuid = await getUserUuid();
    let shouldAddWatermark = true;
    let debugInfo = { userUuid, userCredits: 0, shouldAddWatermark: false };

    if (userUuid) {
      const userCredits = await getUserCredits(userUuid);
      debugInfo.userCredits = userCredits.left_credits;
      // Only add watermark if user has fewer than 5 credits
      shouldAddWatermark = userCredits.left_credits < 5;
      debugInfo.shouldAddWatermark = shouldAddWatermark;
      console.log(`Watermark API: User ${userUuid} has ${userCredits.left_credits} credits, shouldAddWatermark: ${shouldAddWatermark}`);
    }

    // If user has enough credits, return original image without watermark
    if (!shouldAddWatermark) {
      console.log("Returning original image (no watermark needed)");
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Debug': JSON.stringify(debugInfo),
        },
      });
    }

    console.log("Using Cloudflare Pages Function for watermark...");

    // Use Cloudflare Pages Function which supports cf.image.draw
    const baseUrl = new URL(request.url).origin;
    const pagesWatermarkUrl = `${baseUrl}/watermark?src=${encodeURIComponent(src)}`;

    console.log(`Calling Pages Function: ${pagesWatermarkUrl}`);

    const watermarkedResponse = await fetch(pagesWatermarkUrl);

    console.log(`Pages Function response: status=${watermarkedResponse.status}, ok=${watermarkedResponse.ok}`);

    if (watermarkedResponse.ok) {
      console.log("Successfully applied watermark using Pages Function");
      return new NextResponse(watermarkedResponse.body, {
        status: watermarkedResponse.status,
        headers: {
          'Content-Type': 'image/webp',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'true',
          'X-Watermark-Method': 'pages-function-proxy',
          'X-Watermark-Debug': JSON.stringify({...debugInfo, pagesFunction: true}),
        },
      });
    } else {
      console.log("Pages Function failed, returning original image");
      const fallbackResponse = await fetch(src);
      return new NextResponse(fallbackResponse.body, {
        status: fallbackResponse.status,
        headers: {
          'Content-Type': fallbackResponse.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Method': 'pages-function-failed',
          'X-Watermark-Debug': JSON.stringify({...debugInfo, pagesFunction: false}),
        },
      });
    }

  } catch (error) {
    console.error("Error applying watermark:", error);
    
    // Fallback: return original image if watermark fails
    try {
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
        },
      });
    } catch (fallbackError) {
      console.error("Error fetching original image:", fallbackError);
      return new NextResponse("Error processing image", { status: 500 });
    }
  }
}
