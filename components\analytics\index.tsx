// Temporarily disabled analytics components due to missing dependencies
// import GoogleAnalytics from "./google-analytics";
// import OpenPanelAnalytics from "./open-panel";
import PlausibleAnalytics from "./plausible-analytics";

export default function Analytics() {
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  return (
    <>
      {/* <OpenPanelAnalytics /> */}
      {/* <GoogleAnalytics /> */}
      <PlausibleAnalytics />
    </>
  );
}
