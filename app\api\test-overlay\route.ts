import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const src = searchParams.get("src") || "https://picsum.photos/800/600";
  
  try {
    console.log("Testing Cloudflare image overlay...");
    console.log("Source image:", src);

    // Create a simple test overlay using a solid color
    // This tests if Cloudflare's draw functionality works at all
    const overlayResponse = await fetch(src, {
      // @ts-ignore - Cloudflare-specific cf property
      cf: {
        image: {
          format: "webp",
          quality: 85,
          draw: [
            // Simple red rectangle in top-left corner
            {
              url: "data:image/svg+xml;base64," + btoa(`
                <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                  <rect width="100" height="100" fill="red" opacity="0.8"/>
                  <text x="50" y="50" text-anchor="middle" dominant-baseline="middle" fill="white" font-size="12">TEST</text>
                </svg>
              `),
              top: 20,
              left: 20,
              width: 100,
              height: 100
            }
          ],
        },
      },
    });

    console.log(`Overlay response: status=${overlayResponse.status}, ok=${overlayResponse.ok}`);

    if (overlayResponse.ok) {
      return new NextResponse(overlayResponse.body, {
        status: overlayResponse.status,
        headers: {
          'Content-Type': 'image/webp',
          'Cache-Control': 'public, max-age=31536000',
          'X-Overlay-Test': 'success',
          'X-Test-Type': 'red-rectangle-overlay',
        },
      });
    } else {
      return NextResponse.json({
        error: "Cloudflare image overlay failed",
        status: overlayResponse.status,
        statusText: overlayResponse.statusText,
        headers: Object.fromEntries(overlayResponse.headers.entries())
      }, { status: 500 });
    }

  } catch (error) {
    console.error("Error testing overlay:", error);
    return NextResponse.json({
      error: "Failed to test image overlay",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
