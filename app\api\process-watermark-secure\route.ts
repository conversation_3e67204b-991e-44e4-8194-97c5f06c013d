import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from 'uuid';

const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
});

export async function POST(req: NextRequest) {
  try {
    // 检查用户认证
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { taskId } = await req.json();

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    console.log(`Secure watermark processing for task: ${taskId}, user: ${userUuid}`);

    // 验证用户积分
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits > 5) {
      return NextResponse.json(
        { error: 'User has sufficient credits, watermark not required' },
        { status: 403 }
      );
    }

    console.log(`User has ${userCredits.left_credits} credits, proceeding with watermark processing`);

    const supabase = getSupabaseClient();
    
    // 查询数据库获取任务信息
    const { data: taskData, error: taskError } = await supabase
      .from("4o_generations")
      .select("*")
      .eq("task_id", taskId)
      .eq("user_uuid", userUuid)
      .single();

    if (taskError || !taskData) {
      console.error('Task not found:', taskError);
      return NextResponse.json(
        { error: 'Task not found or access denied' },
        { status: 404 }
      );
    }

    // 检查是否已经有水印图片
    if (taskData.generated_image_url && taskData.generated_image_url.includes('watermarked-')) {
      console.log('Watermarked image already exists:', taskData.generated_image_url);
      return NextResponse.json({
        success: true,
        watermarkedUrls: [taskData.generated_image_url],
        message: 'Watermarked image already exists'
      });
    }

    // 简化方案：直接从数据库现有字段获取信息
    let originalImageUrl = null;

    // 方法1：从prompt字段提取URL（如果存在）
    if (taskData.prompt && taskData.prompt.includes('|||REAL_URL:')) {
      try {
        const parts = taskData.prompt.split('|||REAL_URL:');
        if (parts.length > 1) {
          originalImageUrl = parts[1];
          console.log('Extracted original URL from prompt field');
        }
      } catch (parseError) {
        console.error('Error parsing URL from prompt:', parseError);
      }
    }

    // 方法2：如果没有找到，使用original_image_url字段重新生成
    if (!originalImageUrl && taskData.original_image_url) {
      console.log('No stored URL found, will use original_image_url to regenerate');
      // 这里我们可以使用original_image_url重新调用生成API
      // 但为了简化，我们先返回一个错误，让用户重新生成
      return NextResponse.json(
        { error: 'Original generation URL not found. Please regenerate the image.' },
        { status: 404 }
      );
    }

    if (!originalImageUrl) {
      return NextResponse.json(
        { error: 'No original image URL found for watermark processing' },
        { status: 404 }
      );
    }

    console.log('Returning original URL for secure watermark processing:', originalImageUrl.substring(0, 50) + '...');

    // 安全地返回原始URL给前端处理水印
    // 这个API只对积分≤5的用户开放，并且只返回一次
    return NextResponse.json({
      success: true,
      originalUrl: originalImageUrl,
      taskId: taskId,
      message: 'Original URL provided for watermark processing'
    });

  } catch (error) {
    console.error('Error in secure watermark processing:', error);
    return NextResponse.json(
      { 
        error: 'Secure watermark processing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}


