import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const src = searchParams.get("src");

  if (!src) {
    return new NextResponse("Missing ?src= parameter", { status: 400 });
  }

  try {
    // Get user UUID and check credits
    const userUuid = await getUserUuid();
    let shouldAddWatermark = true;

    if (userUuid) {
      const userCredits = await getUserCredits(userUuid);
      // Only add watermark if user has fewer than 5 credits
      shouldAddWatermark = userCredits.left_credits < 5;

      console.log(`User ${userUuid} has ${userCredits.left_credits} credits, shouldAddWatermark: ${shouldAddWatermark}`);
    }

    // If user has enough credits, return original image without watermark
    if (!shouldAddWatermark) {
      console.log("Returning original image (no watermark needed)");
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Reason': 'User has sufficient credits',
        },
      });
    }

    console.log("Applying watermark using images.weserv.nl...");

    // Method 1: Use images.weserv.nl with text overlay and grid pattern
    try {
      // Create watermarked image URL using weserv.nl
      const watermarkParams = new URLSearchParams({
        url: src,
        // Add text watermark
        txt: 'kontext-dev.com',
        txtsize: '24',
        txtcolor: 'ffffff',
        txtbg: '0096ff',
        txtpad: '10',
        txtpos: 'bottom-left',
        // Add grid overlay effect using border
        border: '2,45,666666,0.2',
        // Output format
        output: 'webp',
        q: '85'
      });

      const weservUrl = `https://images.weserv.nl/?${watermarkParams.toString()}`;
      console.log(`Weserv URL: ${weservUrl}`);

      const watermarkedResponse = await fetch(weservUrl);
      console.log(`Weserv response: status=${watermarkedResponse.status}, ok=${watermarkedResponse.ok}`);

      if (watermarkedResponse.ok) {
        console.log("Successfully applied watermark using weserv.nl");
        return new NextResponse(watermarkedResponse.body, {
          status: watermarkedResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'images.weserv.nl',
          },
        });
      }
    } catch (weservError) {
      console.error("Error with weserv.nl:", weservError);
    }

    // Method 2: Fallback to simple text overlay
    try {
      const simpleWatermarkParams = new URLSearchParams({
        url: src,
        txt: 'kontext-dev.com',
        txtsize: '20',
        txtcolor: 'ffffff',
        txtbg: '000000aa',
        txtpad: '8',
        txtpos: 'bottom-left',
        output: 'webp',
        q: '80'
      });

      const simpleWeservUrl = `https://images.weserv.nl/?${simpleWatermarkParams.toString()}`;
      console.log(`Simple weserv URL: ${simpleWeservUrl}`);

      const simpleResponse = await fetch(simpleWeservUrl);
      console.log(`Simple weserv response: status=${simpleResponse.status}, ok=${simpleResponse.ok}`);

      if (simpleResponse.ok) {
        console.log("Successfully applied simple watermark using weserv.nl");
        return new NextResponse(simpleResponse.body, {
          status: simpleResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'images.weserv.nl-simple',
          },
        });
      }
    } catch (simpleError) {
      console.error("Error with simple weserv.nl:", simpleError);
    }

    // Method 3: Try with overlay image (if you have one)
    try {
      const baseUrl = new URL(request.url).origin;
      const overlayUrl = `${baseUrl}/imgs/watermark-grid.png`;

      const overlayParams = new URLSearchParams({
        url: src,
        overlay: overlayUrl,
        oa: '30', // overlay opacity (30%)
        output: 'webp',
        q: '85'
      });

      const overlayWeservUrl = `https://images.weserv.nl/?${overlayParams.toString()}`;
      console.log(`Overlay weserv URL: ${overlayWeservUrl}`);

      const overlayResponse = await fetch(overlayWeservUrl);
      console.log(`Overlay weserv response: status=${overlayResponse.status}, ok=${overlayResponse.ok}`);

      if (overlayResponse.ok) {
        console.log("Successfully applied overlay watermark using weserv.nl");
        return new NextResponse(overlayResponse.body, {
          status: overlayResponse.status,
          headers: {
            'Content-Type': 'image/webp',
            'Cache-Control': 'public, max-age=31536000',
            'X-Watermark-Applied': 'true',
            'X-Watermark-Method': 'images.weserv.nl-overlay',
          },
        });
      }
    } catch (overlayError) {
      console.error("Error with overlay weserv.nl:", overlayError);
    }

    // Fallback: return original image if all watermark methods fail
    console.log("All watermark methods failed, falling back to original image");
    const response = await fetch(src);
    return new NextResponse(response.body, {
      status: response.status,
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000',
        'X-Watermark-Applied': 'false',
        'X-Watermark-Reason': 'All watermark methods failed',
      },
    });

  } catch (error) {
    console.error("Error processing watermark request:", error);

    // Fallback: return original image if anything fails
    try {
      const response = await fetch(src);
      return new NextResponse(response.body, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'false',
          'X-Watermark-Reason': 'General error',
        },
      });
    } catch (fallbackError) {
      console.error("Error fetching original image:", fallbackError);
      return new NextResponse("Error processing image", { status: 500 });
    }
  }
}
