import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { imageData, watermarkText = 'kontext-dev.com' } = await req.json();

    if (!imageData) {
      return NextResponse.json(
        { error: 'Image data is required' },
        { status: 400 }
      );
    }

    // For now, we'll return the original image with a simple SVG watermark overlay
    // This approach works without complex dependencies
    const watermarkedImageData = createSVGWatermark(imageData, watermarkText);

    return NextResponse.json({
      success: true,
      watermarkedImage: watermarkedImageData
    });

  } catch (error) {
    console.error('Error adding watermark:', error);
    return NextResponse.json(
      { error: `Failed to add watermark: ${error.message}` },
      { status: 500 }
    );
  }
}

// Create SVG with watermark overlay that matches original image size
function createSVGWatermark(imageData: string, watermarkText: string): string {
  try {
    // Create a pattern-based watermark that will scale with the image
    const fontSize = 32;
    const spacingX = 300;
    const spacingY = 150;

    // Create watermark pattern that covers the image area
    const svgData = `
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
          <!-- Watermark pattern -->
          <pattern id="watermarkPattern" patternUnits="userSpaceOnUse" width="${spacingX}" height="${spacingY}">
            <text x="${spacingX/2}" y="${spacingY/2}"
                  font-family="Arial, sans-serif"
                  font-size="${fontSize}"
                  font-weight="bold"
                  fill="rgba(255,255,255,0.7)"
                  stroke="rgba(0,0,0,0.5)"
                  stroke-width="1.5"
                  transform="rotate(-45 ${spacingX/2} ${spacingY/2})"
                  text-anchor="middle"
                  dominant-baseline="middle">
              kontext-dev.com
            </text>
          </pattern>

          <!-- Image mask to ensure watermark only appears over the image -->
          <mask id="imageMask">
            <rect width="100%" height="100%" fill="black"/>
            <image href="${imageData}" width="100%" height="100%" preserveAspectRatio="xMidYMid meet" fill="white"/>
          </mask>
        </defs>

        <!-- The image itself -->
        <image href="${imageData}" width="100%" height="100%" preserveAspectRatio="xMidYMid meet"/>

        <!-- Watermark overlay - masked to only appear over the image -->
        <rect width="100%" height="100%" fill="url(#watermarkPattern)" mask="url(#imageMask)" opacity="0.8"/>
      </svg>
    `;

    // Convert SVG to base64
    const svgBase64 = btoa(unescape(encodeURIComponent(svgData)));
    return `data:image/svg+xml;base64,${svgBase64}`;

  } catch (error) {
    console.error('Error in createSVGWatermark:', error);
    throw error;
  }
}


