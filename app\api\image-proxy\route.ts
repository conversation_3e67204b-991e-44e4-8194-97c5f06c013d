import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const imageUrl = searchParams.get("url");

  if (!imageUrl) {
    return new NextResponse("Missing ?url= parameter", { status: 400 });
  }

  try {
    console.log("Image proxy: Fetching image from:", imageUrl);

    // Fetch the image from the provided URL
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    if (!response.ok) {
      console.error(`Image proxy: Failed to fetch image: ${response.status} ${response.statusText}`);
      return new NextResponse(`Failed to fetch image: ${response.status} ${response.statusText}`, { 
        status: response.status 
      });
    }

    const contentType = response.headers.get('Content-Type') || 'image/jpeg';
    const contentLength = response.headers.get('Content-Length');

    console.log(`Image proxy: Successfully fetched image, Content-Type: ${contentType}, Size: ${contentLength} bytes`);

    // Return the image with CORS headers
    return new NextResponse(response.body, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
        'X-Image-Proxy': 'true',
        'X-Original-URL': imageUrl,
      },
    });

  } catch (error) {
    console.error("Image proxy error:", error);
    return new NextResponse(
      `Error fetching image: ${error instanceof Error ? error.message : String(error)}`, 
      { status: 500 }
    );
  }
}
