/**
 * Creates a watermark overlay image that can be used with external services
 * This generates a transparent PNG with diagonal grid pattern
 */
export function createWatermarkOverlay(width: number = 800, height: number = 600): string {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Could not get canvas context');

  // Clear canvas with transparent background
  ctx.clearRect(0, 0, width, height);

  // Create diagonal grid pattern
  ctx.save();
  ctx.globalAlpha = 0.2;
  ctx.strokeStyle = '#666666';
  ctx.lineWidth = 1;

  const spacing = 50;
  const diagonal = Math.sqrt(width * width + height * height);

  // Rotate for diagonal lines
  ctx.translate(width / 2, height / 2);
  ctx.rotate(Math.PI / 4); // 45 degrees
  ctx.translate(-diagonal / 2, -diagonal / 2);

  // Draw grid
  for (let i = 0; i < diagonal; i += spacing) {
    // Vertical lines
    ctx.beginPath();
    ctx.moveTo(i, 0);
    ctx.lineTo(i, diagonal);
    ctx.stroke();

    // Horizontal lines  
    ctx.beginPath();
    ctx.moveTo(0, i);
    ctx.lineTo(diagonal, i);
    ctx.stroke();
  }

  ctx.restore();

  // Return as data URL
  return canvas.toDataURL('image/png');
}

/**
 * Upload watermark overlay to a CDN or static hosting
 * This should be called once to generate the overlay image
 */
export async function generateAndUploadWatermarkOverlay() {
  const overlayDataUrl = createWatermarkOverlay(1000, 1000);
  
  // Convert data URL to blob
  const response = await fetch(overlayDataUrl);
  const blob = await response.blob();
  
  // Here you would upload to your CDN/storage
  // For example, upload to Cloudflare R2, AWS S3, etc.
  console.log('Watermark overlay generated. Upload this to your static assets:');
  console.log('Size:', blob.size, 'bytes');
  
  return overlayDataUrl;
}
