// Cloudflare Pages Function for adding watermark to images
// This function adds a tiled watermark pattern and corner badge to images

export async function onRequestGet(context) {
  const { searchParams } = new URL(context.request.url);
  const src = searchParams.get("src"); // Original image URL from Replicate
  
  if (!src) {
    return new Response("Missing ?src= parameter", { status: 400 });
  }

  // For now, always apply watermark in Pages Function
  // User credit checking will be done in the calling API
  console.log("Applying watermark using Cloudflare Pages Function...");

  // Watermark resources - using your domain for the assets
  const TILE = "https://kontext-dev.com/imgs/watermark_tile.png"; // 45-degree diagonal lines pattern
  const BADGE = "https://kontext-dev.com/imgs/kontext-dev.com_badge.png"; // Corner logo/domain badge

  try {
    // Apply watermark using Cloudflare's image transformation in Pages Function
    console.log(`Applying watermark with TILE: ${TILE}, BADGE: ${BADGE}`);
    
    const watermarkedResponse = await fetch(src, {
      cf: {
        image: {
          // Optional: standardize format and size
          format: "webp",
          quality: 85,
          draw: [
            // 1) Tiled diagonal pattern across entire image
            { 
              url: TILE, 
              repeat: true, 
              opacity: 0.18, 
              rotate: 45 
            },
            // 2) Corner badge in bottom-right
            { 
              url: BADGE, 
              bottom: 20, 
              right: 20, 
              width: 240, 
              fit: "contain", 
              opacity: 0.9 
            }
          ],
        },
      },
    });

    console.log(`Watermark response: status=${watermarkedResponse.status}, ok=${watermarkedResponse.ok}`);

    if (watermarkedResponse.ok) {
      // Add headers to indicate watermark was applied
      const response = new Response(watermarkedResponse.body, {
        status: watermarkedResponse.status,
        headers: {
          'Content-Type': 'image/webp',
          'Cache-Control': 'public, max-age=31536000',
          'X-Watermark-Applied': 'true',
          'X-Watermark-Method': 'pages-function-draw',
        },
      });
      return response;
    } else {
      console.log("Watermark failed, returning original image");
      return fetch(src);
    }

  } catch (error) {
    console.error("Error applying watermark:", error);
    // Fallback: return original image if watermark fails
    return fetch(src);
  }
}
