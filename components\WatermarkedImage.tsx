"use client";

import { useEffect, useRef, useState } from 'react';
import { getWatermarkedImageUrl, shouldApplyWatermark } from '@/utils/watermark';

interface WatermarkedImageProps {
  src: string;
  alt: string;
  shouldAddWatermark?: boolean; // Keep for backward compatibility
  userCredits?: number; // New prop for automatic watermark detection
  className?: string;
  method?: 'api' | 'canvas' | 'weserv'; // Watermark method
  creditThreshold?: number; // Custom threshold
  fill?: boolean;
  width?: number;
  height?: number;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export default function WatermarkedImage({
  src,
  alt,
  shouldAddWatermark,
  userCredits,
  className,
  method = 'api', // Default to API method
  creditThreshold = 5,
  fill,
  width,
  height,
  sizes,
  priority,
  quality,
  placeholder,
  blurDataURL,
  onLoad,
  onError,
  onClick,
  style
}: WatermarkedImageProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    // Determine if watermark should be applied
    let needsWatermark = false;

    if (shouldAddWatermark !== undefined) {
      // Use explicit prop if provided (backward compatibility)
      needsWatermark = shouldAddWatermark;
    } else {
      // Use credit-based detection
      // If userCredits is null/undefined, treat as 0 credits (should show watermark)
      const credits = userCredits ?? 0;
      needsWatermark = shouldApplyWatermark(credits, creditThreshold);
    }

    console.log('WatermarkedImage:', {
      src: src.substring(0, 50) + '...',
      needsWatermark,
      userCredits,
      credits: userCredits ?? 0,
      method
    });

    if (!needsWatermark) {
      setProcessedImageUrl(src);
      setLoading(false);
      setError('');
      return;
    }

    // Apply watermark based on method
    if (method === 'api') {
      // Use our watermark API
      const watermarkedUrl = `/api/watermark-simple?src=${encodeURIComponent(src)}`;
      setProcessedImageUrl(watermarkedUrl);
      setLoading(false);
      setError('');
    } else if (method === 'weserv') {
      // Use weserv.nl directly
      const watermarkedUrl = getWatermarkedImageUrl(src, userCredits || 0, creditThreshold);
      setProcessedImageUrl(watermarkedUrl);
      setLoading(false);
      setError('');
    } else if (method === 'canvas') {
      // Use canvas method (original implementation)
      addWatermarkCanvas();
    }

    // Cleanup function
    return () => {
      if (processedImageUrl && processedImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(processedImageUrl);
      }
    };
  }, [src, shouldAddWatermark, userCredits, method, creditThreshold]);

  const addWatermarkCanvas = async () => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      setLoading(true);
      setError('');

      // Load the main image
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        // Set canvas size to match image
        canvas.width = img.width;
        canvas.height = img.height;

        // Draw the main image
        ctx.drawImage(img, 0, 0);

        // Create diagonal text watermark pattern
        addDiagonalTextWatermark(ctx, canvas.width, canvas.height);

        // Convert to blob and create URL
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            setProcessedImageUrl(url);
            setLoading(false);
          }
        }, 'image/webp', 0.85);
      };

      img.onerror = () => {
        console.error('Failed to load image for canvas watermark');
        setProcessedImageUrl(src);
        setLoading(false);
        setError('Failed to load image');
      };

      img.src = src;
    } catch (error) {
      console.error('Error adding canvas watermark:', error);
      setProcessedImageUrl(src);
      setLoading(false);
      setError('Canvas watermark failed');
    }
  };

  const addDiagonalTextWatermark = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Create diagonal text watermark pattern like kontext-dev.com
    ctx.save();

    // Set text properties
    const fontSize = Math.max(16, Math.min(width, height) * 0.03); // Responsive font size
    ctx.font = `${fontSize}px Arial, sans-serif`;
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = 0.3; // Semi-transparent
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';

    // Calculate text dimensions
    const watermarkText = 'kontext-dev.com';
    const textMetrics = ctx.measureText(watermarkText);
    const textWidth = textMetrics.width;
    const textHeight = fontSize;

    // Calculate spacing for diagonal pattern
    const diagonalSpacing = Math.max(textWidth + 50, 150);
    const verticalSpacing = Math.max(textHeight + 30, 80);

    // Rotation angle (45 degrees)
    const rotationAngle = -Math.PI / 4;

    // Create diagonal grid pattern covering entire canvas
    const maxDimension = Math.max(width, height);
    const gridExtend = maxDimension * 1.5;

    for (let x = -gridExtend; x < width + gridExtend; x += diagonalSpacing) {
      for (let y = -gridExtend; y < height + gridExtend; y += verticalSpacing) {
        ctx.save();
        ctx.translate(x, y);
        ctx.rotate(rotationAngle);
        ctx.fillText(watermarkText, 0, 0);
        ctx.restore();
      }
    }

    ctx.restore();
  };



  const handleImageError = () => {
    console.error('Watermarked image failed to load, falling back to original');
    setProcessedImageUrl(src);
    setError('Watermark failed, showing original');
  };

  if (loading) {
    return (
      <div className={`${className} bg-gray-200 animate-pulse flex items-center justify-center`}>
        <span className="text-gray-500">
          {method === 'canvas' ? 'Processing...' : 'Loading...'}
        </span>
      </div>
    );
  }

  if (error && !processedImageUrl) {
    return (
      <div className={`${className} bg-red-100 flex items-center justify-center`}>
        <span className="text-red-500 text-sm">{error}</span>
      </div>
    );
  }

  const imageProps = {
    src: processedImageUrl,
    alt,
    className,
    onLoad: () => {
      setLoading(false);
      onLoad?.();
    },
    onError: () => {
      handleImageError();
      onError?.();
    },
    onClick,
    style,
    quality,
    placeholder,
    blurDataURL,
    priority,
    ...(fill ? { fill: true } : { width, height }),
    ...(sizes && { sizes })
  };

  return (
    <>
      <canvas ref={canvasRef} style={{ display: 'none' }} />
      <img {...imageProps} />
      {/* Debug info in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-500 mt-1">
          Method: {method} | Credits: {userCredits} | {error && `Error: ${error}`}
        </div>
      )}
    </>
  );
}

// Export utility function for direct use
export { getWatermarkedImageUrl, shouldApplyWatermark } from '@/utils/watermark';
