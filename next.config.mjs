import createNextIntlPlugin from "next-intl/plugin";

// Conditionally import bundle analyzer only if available
let withBundleAnalyzer = (config) => config;
try {
  const bundleAnalyzer = await import("@next/bundle-analyzer");
  withBundleAnalyzer = bundleAnalyzer.default({
    enabled: process.env.ANALYZE === "true",
  });
} catch (error) {
  console.log("@next/bundle-analyzer not found, skipping bundle analysis");
}

// Conditionally import MDX only if available
let withMDX = (config) => config;
try {
  const mdx = await import("@next/mdx");
  withMDX = mdx.default({
    options: {
      remarkPlugins: [],
      rehypePlugins: [],
    },
  });
} catch (error) {
  console.log("@next/mdx not found, skipping MDX support");
}

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*",
      },
    ],
  },
  async redirects() {
    return [];
  },
  webpack: (config, { isServer }) => {
    config.optimization.minimize = true;

    return config;
  },
};

// Make sure experimental mdx flag is enabled if MDX is available
const configWithMDX = {
  ...nextConfig,
  experimental: {
    mdxRs: withMDX !== ((config) => config), // Only enable if MDX is available
  },
};

export default withBundleAnalyzer(withNextIntl(withMDX(configWithMDX)));
